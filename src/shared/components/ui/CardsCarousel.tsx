import { Typography } from '@components/typography';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import { useCallback, useEffect, useRef } from 'react';

type DealsFeatureCarouselProps<T extends { id?: string | number }> = {
  cards: T[];
  title: React.ReactNode;
  actionBtn?: React.ReactNode;
  renderCard: (card: T) => React.ReactNode;
  hasNextPage?: boolean;
  fetchNextPage?: () => void; // Callback to fetch more items
  isFetchingNextPage?: boolean;
};

export const CardsCarousel = <T extends { id?: string | number }>({
  title,
  cards,
  actionBtn,
  renderCard,
  hasNextPage,
  fetchNextPage,
  isFetchingNextPage,
}: DealsFeatureCarouselProps<T>) => {
  const isMobileView = useIsMobileView();
  const carouselApiRef = useRef<CarouselApi>();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const triggerRef = useRef<HTMLElement | null>(null);
  const selectedIndexRef = useRef<number>(0); // Store the currently visible slide index

  // Track the selected slide index
  useEffect(() => {
    const api = carouselApiRef.current;
    if (!api) return;

    const updateSelectedIndex = () => {
      selectedIndexRef.current = api.selectedScrollSnap();
    };

    api.on('select', updateSelectedIndex);
    api.on('reInit', updateSelectedIndex);

    // Initial update
    updateSelectedIndex();

    return () => {
      api.off('select', updateSelectedIndex);
      api.off('reInit', updateSelectedIndex);
    };
  }, []);

  // Restore scroll position after new items are added
  useEffect(() => {
    const api = carouselApiRef.current;
    if (!api) return;

    // If new items were added
    if (cards.length > previousCardsLengthRef.current) {
      // Reinitialize carousel to account for new slides
      api.reInit();

      // Restore the previous scroll position
      requestAnimationFrame(() => {
        api.scrollTo(selectedIndexRef.current, false); // Scroll to the previously selected slide
      });
    }

    previousCardsLengthRef.current = cards.length;
  }, [cards.length]);

  // Set up IntersectionObserver to trigger lazy loading
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      if (entry.isIntersecting && hasNextPage && !isFetchingNextPage) {
        fetchNextPage?.();
      }
    },
    [hasNextPage, isFetchingNextPage, fetchNextPage],
  );

  useEffect(() => {
    if (triggerRef.current && hasNextPage) {
      observerRef.current = new IntersectionObserver(handleObserver, {
        root: null, // Use the viewport as the root
        rootMargin: '200px', // Trigger when the sentinel is 200px from the viewport edge
        threshold: 0.1, // Trigger when 10% of the sentinel is visible
      });

      observerRef.current.observe(triggerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasNextPage, handleObserver]);

  const previousCardsLengthRef = useRef(cards.length);

  if (!cards?.length) {
    return null;
  }

  return (
    <Carousel
      className={'w-full overflow-hidden'}
      opts={{
        loop: false, // Disable looping to avoid issues with lazy loading
        align: 'start',
        dragFree: true,
      }}
      setApi={(api) => {
        carouselApiRef.current = api;
      }}
    >
      <div
        className={'mb-6 md:mb-2 grid grid-flow-col items-center px-6 md:px-0'}
      >
        <Typography
          tag="h2"
          variant={isMobileView ? 'xxs' : 'xs'}
          className={'flex items-center gap-3'}
        >
          {title}
        </Typography>

        <div className={'relative ml-auto h-8'}>
          {actionBtn}

          {!isMobileView && (
            <>
              <CarouselPrevious
                className={cn(
                  actionBtn ? '!-left-[5.7rem]' : '!-left-[4.7rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
              <CarouselNext
                className={cn(
                  actionBtn ? '!-left-[3rem]' : '!-left-[2rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
            </>
          )}
        </div>
      </div>
      <CarouselContent
        isInteractive
        containerClassName="pl-6 md:pl-0"
        className="-ml-6 mr-5 flex py-4"
      >
        {cards.map((card, index) => (
          <CarouselItem
            key={card.id ?? index}
            className="basis-[15.75rem] pl-6"
          >
            {renderCard(card)}
          </CarouselItem>
        ))}

        {hasNextPage && (
          <CarouselItem className="basis-auto pl-6">
            <div
              ref={triggerRef}
              className="flex h-full w-16 items-center justify-center"
              style={{
                minWidth: '64px',
                visibility: 'visible',
                display: 'flex',
              }}
              data-testid="horizontal-infinite-scroll-trigger"
            >
              {isFetchingNextPage && (
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
              )}
            </div>
          </CarouselItem>
        )}
      </CarouselContent>
    </Carousel>
  );
};
